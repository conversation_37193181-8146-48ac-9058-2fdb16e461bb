<!--
  页面名称：题库管理
  功能描述：展示题库列表，支持多级分类筛选、题型、业务模块、题干内容搜索，支持分页、操作下拉菜单
-->
<template>
  <div v-if="!showSortMgt">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg1" v-loading="statisticsLoading">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon1"><QuestionFilled /></el-icon>
            <div>
              <div class="stat-num">{{ stat.total }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg2" v-loading="statisticsLoading">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon2"><Tickets /></el-icon>
            <div>
              <div class="stat-num">{{ stat.single }}</div>
              <div class="stat-label">选择题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg3" v-loading="statisticsLoading">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon3"><CircleCheck /></el-icon>
            <div>
              <div class="stat-num">{{ stat.judge }}</div>
              <div class="stat-label">判断题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg4" v-loading="statisticsLoading">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon4"><Edit /></el-icon>
            <div>
              <div class="stat-num">{{ stat.shortAnswer }}</div>
              <div class="stat-label">简答题</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <div class="search-fields">
        <el-form-item label="一级名称：">
          <el-select
            v-model="searchForm.level1"
            placeholder="全部一级"
            style="width: 120px"
            :loading="categoryLoading"
            clearable
            @change="onLevel1Change"
          >
            <el-option label="全部一级" value="" />
            <el-option
              v-for="item in level1Options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="二级名称：">
          <el-select
            v-model="searchForm.level2"
            placeholder="全部二级"
            style="width: 120px"
            :loading="categoryLoading"
            clearable
            @change="onLevel2Change"
          >
            <el-option label="全部二级" value="" />
            <el-option
              v-for="item in level2Options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="三级名称：">
          <el-select
            v-model="searchForm.level3"
            placeholder="全部三级"
            style="width: 120px"
            :loading="categoryLoading"
            clearable
            @change="onLevel3Change"
          >
            <el-option label="全部三级" value="" />
            <el-option
              v-for="item in level3Options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题型：" class="type-select-item">
          <el-select v-model="searchForm.type" placeholder="全部题型" style="width: 100px">
            <el-option label="全部题型" value="" />
            <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务模块：">
          <el-select v-model="searchForm.biz" placeholder="全部" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option
              v-for="item in bizOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索题干内容..."
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">筛选</el-button>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-user-solid" @click="onShowSortMgt">分类管理</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" @click="downloadTemplate">下载模板</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-upload" @click="batchImportVisible = true"
            >批量导入</el-button
          >
        </el-form-item>
      </div>
    </el-form>

    <!-- 表格区加横向滚动 -->
    <div class="table-responsive">
      <el-table :data="tableData" border style="min-width: 1100px; width: 100%">
        <el-table-column prop="level1Name" label="一级名称" min-width="100" />
        <el-table-column prop="level2Name" label="二级名称" min-width="100" />
        <el-table-column prop="level3Name" label="三级名称" min-width="100" />
        <el-table-column prop="certName" label="认定点名称" min-width="120" />
        <el-table-column prop="title" label="题干" min-width="200" />
        <el-table-column prop="type" label="题型" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.type === '单选题'" type="primary">单选题</el-tag>
            <el-tag v-else-if="scope.row.type === '多选题'" type="success">多选题</el-tag>
            <el-tag v-else-if="scope.row.type === '判断题'" type="warning">判断题</el-tag>
            <el-tag v-else-if="scope.row.type === '简答题'" type="danger">简答题</el-tag>
            <el-tag v-else>{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="biz" label="业务模块" min-width="100" />
        <el-table-column prop="answer" label="参考答案" min-width="200" show-overflow-tooltip />
        <el-table-column prop="creatorName" label="创建人" min-width="80" />
        <el-table-column prop="createTime" label="创建时间" min-width="100" />
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="scope">
            <el-dropdown @command="handleCommand(scope.row, $event)">
              <el-button size="small" type="primary">
                操作 <el-icon class="el-icon--right"><i class="el-icon-arrow-down"></i></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                  <el-dropdown-item command="log">操作日志</el-dropdown-item>
                  <el-dropdown-item command="delete" style="color: #ff4d4f">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页区 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, prev, pager, next, jumper"
      @current-change="fetchList"
      style="margin-top: 16px; text-align: right"
    />
    <AddQuestion
      v-model:visible="editVisible"
      :form-data="editForm"
      @success="onAddQuestionSuccess"
    />
    <!-- 操作日志抽屉 -->
    <QuestionOptLog ref="optLogRef" />
    <!-- 批量导入抽屉 -->
    <el-drawer v-model="batchImportVisible" direction="rtl" size="500px" :with-header="false">
      <BatchImportQuestion @close="batchImportVisible = false" @submit="onBatchImportSubmit" />
    </el-drawer>
  </div>
  <div v-else>
    <SortManagement @back="onBackSortMgt" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { QuestionFilled, Tickets, CircleCheck, Edit } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddQuestion from './components/AddQuestion.vue'
import QuestionOptLog from './components/QuestionOptLog.vue'
import SortManagement from './components/SortManagement.vue'
import BatchImportQuestion from './components/BatchImportQuestion.vue'
import {
  QuestionManagementApi,
  type Question,
  type QuestionPageParams,
  type QuestionStatisticsResult
} from '@/api/infra/questionManagement'
import { getDictDataPage } from '@/api/system/dict/dict.data'
const batchImportVisible = ref(false)
const batchImportLoading = ref(false)

// 批量导入考题
async function onBatchImportSubmit(file: File) {
  try {
    batchImportLoading.value = true
    const result = await QuestionManagementApi.batchImportQuestions(file)

    if (result.failed > 0) {
      // 有失败的记录，显示详细信息
      let message = `导入完成！成功：${result.success}条，失败：${result.failed}条`
      if (result.failedList && result.failedList.length > 0) {
        message += '\n失败详情：\n'
        result.failedList.slice(0, 5).forEach((item) => {
          message += `第${item.row}行：${item.reason}\n`
        })
        if (result.failedList.length > 5) {
          message += `...还有${result.failedList.length - 5}条失败记录`
        }
      }
      ElMessage.warning(message)
    } else {
      ElMessage.success(`批量导入成功！共导入${result.success}条考题`)
    }

    batchImportVisible.value = false
    // 刷新列表和统计数据
    fetchQuestionList()
    fetchQuestionStatistics()
  } catch (error) {
    console.error('批量导入失败:', error)
    ElMessage.error('批量导入失败，请重试')
  } finally {
    batchImportLoading.value = false
  }
}

/** 统计卡片数据 */
const stat = ref({ total: 0, single: 0, judge: 0, shortAnswer: 0 })
const statisticsLoading = ref(false)

/** 搜索表单数据 */
const searchForm = ref({
  level1: '',
  level2: '',
  level3: '',
  type: '',
  biz: '',
  keyword: ''
})

/** 分类数据相关 */
const categoryLoading = ref(false)
const allCategories = ref<any[]>([]) // 存储所有分类数据

/** 动态下拉选项 */
const level1Options = ref<Array<{ label: string; value: string; data: any }>>([])
const level2Options = ref<Array<{ label: string; value: string; data: any }>>([])
const level3Options = ref<Array<{ label: string; value: string; data: any }>>([])

/** 获取分类数据 */
const fetchCategories = async () => {
  try {
    categoryLoading.value = true
    const result = await QuestionManagementApi.getCategoryList()

    // 处理不同的返回数据格式
    let categoryList: any[] = []
    if (Array.isArray(result)) {
      categoryList = result
    } else if (result?.list && Array.isArray(result.list)) {
      categoryList = result.list
    }

    allCategories.value = categoryList
    console.log('获取到的全量分类数据:', categoryList)

    // 生成一级分类选项
    const level1Map = new Map()
    categoryList.forEach((category) => {
      if (category.level1Name && !level1Map.has(category.level1Name)) {
        level1Map.set(category.level1Name, {
          label: category.level1Name,
          value: category.level1Name,
          data: category
        })
      }
    })
    level1Options.value = Array.from(level1Map.values())

    // 如果一级名称未选择，显示所有二级分类选项
    if (!searchForm.value.level1) {
      const level2Map = new Map()
      categoryList.forEach((category) => {
        if (category.level2Name && !level2Map.has(category.level2Name)) {
          level2Map.set(category.level2Name, {
            label: category.level2Name,
            value: category.level2Name,
            data: category
          })
        }
      })
      level2Options.value = Array.from(level2Map.values())
    }

    // 如果二级名称未选择，显示所有三级分类选项
    if (!searchForm.value.level2) {
      const level3Map = new Map()
      categoryList.forEach((category) => {
        if (category.level3Name && !level3Map.has(category.level3Name)) {
          level3Map.set(category.level3Name, {
            label: category.level3Name,
            value: category.level3Name,
            data: category
          })
        }
      })
      level3Options.value = Array.from(level3Map.values())
    }
  } catch (error) {
    console.error('获取分类数据失败:', error)
    ElMessage.error('获取分类数据失败')
  } finally {
    categoryLoading.value = false
  }
}
/** 一级分类变化处理 */
const onLevel1Change = (value: string) => {
  // 清空下级选择
  searchForm.value.level2 = ''
  searchForm.value.level3 = ''

  if (!value) {
    // 如果一级名称未选择，显示所有二级分类选项
    const level2Map = new Map()
    allCategories.value.forEach((category) => {
      if (category.level2Name && !level2Map.has(category.level2Name)) {
        level2Map.set(category.level2Name, {
          label: category.level2Name,
          value: category.level2Name,
          data: category
        })
      }
    })
    level2Options.value = Array.from(level2Map.values())

    // 显示所有三级分类选项
    const level3Map = new Map()
    allCategories.value.forEach((category) => {
      if (category.level3Name && !level3Map.has(category.level3Name)) {
        level3Map.set(category.level3Name, {
          label: category.level3Name,
          value: category.level3Name,
          data: category
        })
      }
    })
    level3Options.value = Array.from(level3Map.values())
    return
  }

  // 生成对应一级分类的二级分类选项
  const level2Map = new Map()
  allCategories.value.forEach((category) => {
    if (
      category.level1Name === value &&
      category.level2Name &&
      !level2Map.has(category.level2Name)
    ) {
      level2Map.set(category.level2Name, {
        label: category.level2Name,
        value: category.level2Name,
        data: category
      })
    }
  })
  level2Options.value = Array.from(level2Map.values())

  // 由于二级名称已被清空，根据二级名称联动逻辑，应显示所有三级分类选项
  const level3Map = new Map()
  allCategories.value.forEach((category) => {
    if (category.level3Name && !level3Map.has(category.level3Name)) {
      level3Map.set(category.level3Name, {
        label: category.level3Name,
        value: category.level3Name,
        data: category
      })
    }
  })
  level3Options.value = Array.from(level3Map.values())
}

/** 二级分类变化处理 */
const onLevel2Change = (value: string) => {
  // 清空下级选择
  searchForm.value.level3 = ''

  if (!value) {
    // 如果二级名称未选择（为空），则显示所有三级分类选项
    const level3Map = new Map()
    allCategories.value.forEach((category) => {
      if (category.level3Name && !level3Map.has(category.level3Name)) {
        level3Map.set(category.level3Name, {
          label: category.level3Name,
          value: category.level3Name,
          data: category
        })
      }
    })
    level3Options.value = Array.from(level3Map.values())
    return
  }

  // 如果二级名称已选择，只显示属于该二级分类的三级选项
  const level3Map = new Map()
  allCategories.value.forEach((category) => {
    if (
      category.level2Name === value &&
      category.level3Name &&
      !level3Map.has(category.level3Name)
    ) {
      level3Map.set(category.level3Name, {
        label: category.level3Name,
        value: category.level3Name,
        data: category
      })
    }
  })
  level3Options.value = Array.from(level3Map.values())
}

/** 三级分类变化处理 */
const onLevel3Change = (value: string) => {
  // 三级分类变化时不需要特殊处理，只是为了保持一致性
  console.log('三级分类变化:', value)
}

const typeOptions = [
  '单选题',
  '多选题',
  '判断题',
  '简答题',
  '填空题',
  '材料题',
  '排序题',
  '匹配题',
  '文件上传题'
]
const bizOptions = ref<any[]>([{ label: '全部', value: '' }])

/** 表格数据和加载状态 */
const tableData = ref<Question[]>([])
const loading = ref(false)

/** 分页信息 */
const pagination = ref({ page: 1, size: 10, total: 0 })

// 获取考题统计数据
const fetchQuestionStatistics = async () => {
  try {
    statisticsLoading.value = true
    const params: any = {}

    // 添加筛选条件
    if (searchForm.value.biz) {
      params.biz = searchForm.value.biz
    }
    if (searchForm.value.level1) {
      params.level1Name = searchForm.value.level1
    }
    if (searchForm.value.level2) {
      params.level2Name = searchForm.value.level2
    }
    if (searchForm.value.level3) {
      params.level3Name = searchForm.value.level3
    }

    const result = await QuestionManagementApi.getQuestionStatistics(params)

    // 更新统计卡片数据
    stat.value.total = result.total
    stat.value.single = result.single
    stat.value.judge = result.judge
    stat.value.shortAnswer = result.shortAnswer || 0
  } catch (error) {
    console.error('获取考题统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请重试')
  } finally {
    statisticsLoading.value = false
  }
}

// 获取考题列表数据
const fetchQuestionList = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: QuestionPageParams = {
      pageNo: pagination.value.page,
      pageSize: pagination.value.size
    }

    // 添加筛选条件
    if (searchForm.value.level1) {
      params.level1Name = searchForm.value.level1
    }
    if (searchForm.value.level2) {
      params.level2Name = searchForm.value.level2
    }
    if (searchForm.value.level3) {
      params.level3Name = searchForm.value.level3
    }
    if (searchForm.value.type) {
      params.type = searchForm.value.type
    }
    if (searchForm.value.biz) {
      params.biz = searchForm.value.biz
    }
    if (searchForm.value.keyword) {
      params.keyword = searchForm.value.keyword
    }

    // 调用API获取数据
    const result = await QuestionManagementApi.getQuestionPage(params)

    tableData.value = result.list
    pagination.value.total = result.total
  } catch (error) {
    console.error('获取考题列表失败:', error)
    ElMessage.error('获取考题列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 搜索方法
const onSearch = () => {
  pagination.value.page = 1 // 重置到第一页
  fetchQuestionList()
}

// 分页变更方法
const fetchList = (page?: number) => {
  if (page) {
    pagination.value.page = page
  }
  fetchQuestionList()
}

// 页面大小变更方法
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1 // 重置到第一页
  fetchQuestionList()
}

// 获取业务模块字典数据
const fetchBusinessModuleOptions = async () => {
  try {
    const res = await getDictDataPage({ dictType: 'business_module', pageNo: 1, pageSize: 100 })
    bizOptions.value = [
      { label: '全部', value: '' },
      ...(res?.list || []).map((item) => ({
        label: item.label,
        value: item.value
      }))
    ]
  } catch (error) {
    console.error('获取业务模块字典数据失败:', error)
  }
}

const editVisible = ref(false)
const editForm = ref({})

// 新增考题
const onAddQuestion = () => {
  editForm.value = {} // 清空表单数据
  editVisible.value = true
}

const onEdit = (row: any) => {
  editForm.value = { ...row }
  editVisible.value = true
}

// 删除考题
const onDelete = async (row: Question) => {
  try {
    // 确认对话框
    await ElMessageBox.confirm(`确定要删除考题"${row.title}"吗？删除后将无法恢复。`, '确认删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await QuestionManagementApi.deleteQuestion(row.id!)
    ElMessage.success('删除成功')

    // 刷新列表和统计数据
    fetchQuestionList()
    fetchQuestionStatistics()
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作，不显示错误信息
      return
    }
    console.error('删除考题失败:', error)
    ElMessage.error('删除失败，请重试')
  }
}

const onDetail = (row: any) => {
  // TODO: 详情逻辑
}

const onCopy = (row: any) => {
  console.log('开始复制考题:', row)

  // 深拷贝原考题数据
  const copyData = JSON.parse(JSON.stringify(row))

  // 清除不应该复制的字段
  delete copyData.id
  delete copyData.createTime
  delete copyData.updateTime
  delete copyData.creator
  delete copyData.creatorName
  delete copyData.updater
  delete copyData.updaterName

  // 在题目标题后添加"（副本）"标识
  if (copyData.title) {
    copyData.title = copyData.title + '（副本）'
  }

  console.log('复制后的考题数据（已清除id等字段）:', copyData)
  console.log('复制模式：将作为新增考题处理')

  // 设置为新增模式，传入复制的数据
  editForm.value = copyData
  editVisible.value = true
}

// 新增/编辑考题成功回调
const onAddQuestionSuccess = () => {
  editVisible.value = false
  // 刷新列表和统计数据
  fetchQuestionList()
  fetchQuestionStatistics()
}

// 操作日志相关
const optLogRef = ref()

const onLog = (row: any) => {
  console.log('查看考题操作日志:', row)
  if (!row.id) {
    ElMessage.error('考题ID缺失，无法查看操作日志')
    return
  }

  // 获取考题标题，限制长度
  const title = row.title
    ? row.title.length > 20
      ? row.title.substring(0, 20) + '...'
      : row.title
    : '考题'

  // 打开操作日志抽屉
  optLogRef.value && optLogRef.value.open(title, row.id)
}

const showSortMgt = ref(false)

const onShowSortMgt = () => {
  showSortMgt.value = true
}
const onBackSortMgt = () => {
  showSortMgt.value = false
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const blob = await QuestionManagementApi.downloadTemplate()

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '考题导入模板.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  }
}

function handleCommand(row: any, command: string) {
  if (command === 'edit') onEdit(row)
  else if (command === 'copy') onCopy(row)
  else if (command === 'log') onLog(row)
  else if (command === 'delete') onDelete(row)
}

// 监听一级名称变化，重新加载二级和三级选项
watch(
  () => searchForm.value.level1,
  (newValue, oldValue) => {
    if (newValue !== oldValue && allCategories.value.length > 0) {
      onLevel1Change(newValue)
    }
  }
)

// 监听二级名称变化，重新加载三级选项
watch(
  () => searchForm.value.level2,
  (newValue, oldValue) => {
    if (newValue !== oldValue && allCategories.value.length > 0) {
      onLevel2Change(newValue)
    }
  }
)

// 页面加载时获取数据
onMounted(() => {
  fetchCategories() // 获取分类数据
  fetchBusinessModuleOptions() // 获取业务模块数据
  fetchQuestionStatistics() // 获取统计数据
  fetchQuestionList() // 获取考题列表
})
</script>

<style scoped lang="scss">
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.mb-2 {
  margin-bottom: 12px;
}
.search-form {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-bottom: 12px;
  .el-form-item {
    margin-bottom: 0;
    margin-right: 4px;
  }
  .btn-group {
    display: flex;
    align-items: center;
    gap: 2px;
    margin-left: 4px;
    white-space: nowrap;
  }
}
.search-fields {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}
.search-btns {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  align-items: center;
  justify-content: flex-start;
}
.search-form-actions {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.table-responsive {
  width: 100%;
  overflow-x: auto;
}
@media (max-width: 1400px) {
  .table-responsive {
    min-width: 1100px;
  }
}
@media (max-width: 1200px) {
  .stat-card-inner {
    gap: 8px;
  }
  .stat-num {
    font-size: 15px;
  }
  .stat-label {
    font-size: 11px;
  }
  .el-table th,
  .el-table td {
    font-size: 13px;
  }
  .search-form {
    gap: 4px 0;
  }
  .btn-group .el-button {
    margin-bottom: 2px;
  }
  .search-form-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-fields {
    flex-wrap: wrap;
    gap: 4px;
  }
  .search-btns {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-btns .el-button {
    width: 100%;
  }
}
@media (max-width: 900px) {
  .stat-card-inner {
    gap: 4px;
  }
  .stat-num {
    font-size: 13px;
  }
  .stat-label {
    font-size: 10px;
  }
  .el-table th,
  .el-table td {
    font-size: 12px;
  }
  .search-form {
    flex-wrap: wrap;
    flex-direction: column;
    gap: 2px 0;
  }
  .table-responsive {
    min-width: 700px;
  }
  .btn-group {
    flex-wrap: wrap;
    flex-direction: column;
  }
  .btn-group .el-button {
    width: 100%;
    margin-bottom: 2px;
  }
  .search-fields {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
.search-row {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  justify-content: flex-start;
}
.search-row-actions {
  align-items: flex-start;
}
.btn-group {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 0;
}
// 新增紧凑按钮组样式
.compact-btn-group {
  gap: 0;
}
.delete-btn {
  background-color: #ff4d4f !important;
  color: #fff !important;
  border: none !important;
}
@media (max-width: 1200px) {
  .search-row,
  .search-row-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .btn-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    width: 100%;
  }
  .btn-group .el-button {
    width: 100%;
  }
}
.type-select-item {
  margin-left: -8px;
}
@media (max-width: 1200px) {
  .type-select-item {
    margin-left: 0;
  }
}
</style>
