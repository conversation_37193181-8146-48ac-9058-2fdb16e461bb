import request from '@/config/axios'

// ==================== 考题管理相关接口类型定义 ====================

// 考题选项类型定义
export interface QuestionOption {
  optionKey?: string // 选项标识：A、B、C、D等
  optionContent?: string // 选项内容
  isCorrect?: boolean // 是否正确答案
  optionType?: string // 选项类型：choice-选择项，matchLeft-匹配左列，matchRight-匹配右列
  sortOrder?: number // 排序序号
}

// 考题基础信息类型定义
export interface Question {
  id?: number
  level1Name: string // 一级分类名称
  level1Code: string // 一级分类代码
  level2Name: string // 二级分类名称
  level2Code: string // 二级分类代码
  level3Name: string // 三级分类名称
  level3Code: string // 三级分类代码
  certName: string // 认定点名称
  certCode: string // 认定点代码
  title: string // 题干内容
  type: string // 题型
  answer: string // 参考答案
  biz: string // 业务模块
  bizName?: string // 业务模块名称
  difficulty?: number // 难度等级：1-简单，2-中等，3-困难
  score?: number // 题目分值
  options?: QuestionOption[] // 选项列表
  creator?: string // 创建人
  creatorName?: string // 创建人姓名
  createTime?: string // 创建时间
}

// 新增考题请求参数
export interface AddQuestionParams {
  level1Name: string
  level1Code: string
  level2Name: string
  level2Code: string
  level3Name: string
  level3Code: string
  certName: string
  certCode: string
  title: string
  type: string
  answer: string
  biz: string
  bizName?: string
  difficulty?: number
  score?: number
  options?: QuestionOption[]
}

// 新增考题响应结果
export interface AddQuestionResult {
  id: number
}

// 更新考题请求参数
export interface UpdateQuestionParams extends AddQuestionParams {
  id: number
}

// 考题分页查询参数
export interface QuestionPageParams {
  pageNo: number
  pageSize: number
  level1Name?: string // 一级分类名称
  level2Name?: string // 二级分类名称
  level3Name?: string // 三级分类名称
  type?: string // 题型
  biz?: string // 业务模块
  bizName?: string // 业务模块名称
  keyword?: string // 题干内容关键词搜索
}

// 考题分页查询结果
export interface QuestionPageResult {
  total: number
  list: Question[]
}

// 考题统计结果
export interface QuestionStatisticsResult {
  total: number // 题目总数
  single: number // 选择题数（单选+多选）
  judge: number // 判断题数
  shortAnswer: number // 简答题数
  fill: number // 填空题数
  material: number // 材料题数
  sort: number // 排序题数
  match: number // 匹配题数
  upload: number // 文件上传题数
}

// 批量导入结果
export interface BatchImportResult {
  total: number // 总数
  success: number // 成功数
  failed: number // 失败数
  failedList: Array<{
    row: number // 行号
    reason: string // 失败原因
  }>
}

// ==================== 考题分类管理相关接口类型定义 ====================

// 考题分类类型定义
export interface QuestionCategory {
  id?: number
  level1Name: string // 一级分类名称
  level1Code: string // 一级分类代码
  level2Name?: string // 二级分类名称
  level2Code?: string // 二级分类代码
  level3Name?: string // 三级分类名称
  level3Code?: string // 三级分类代码
  certName?: string // 认定点名称
  certCode?: string // 认定点代码
  biz: string // 业务模块
  bizName?: string // 业务模块名称
  parentId?: number // 父级分类ID
  level: number // 分类层级：1-一级，2-二级，3-三级
  creator?: string // 创建人
  creatorName?: string // 创建人姓名
  createTime?: string // 创建时间
}

// 新增分类请求参数
export interface AddCategoryParams {
  level1Name: string
  level1Code: string
  level2Name?: string
  level2Code?: string
  level3Name?: string
  level3Code?: string
  certName?: string
  certCode?: string
  biz: string
  bizName?: string
  parentId?: number | null
  level: number
}

// 新增分类响应结果
export interface AddCategoryResult {
  id: number
}

// 更新分类请求参数
export interface UpdateCategoryParams {
  id: number
  level1Name: string
  level1Code: string
  level2Name?: string
  level2Code?: string
  level3Name?: string
  level3Code?: string
  certName?: string
  certCode?: string
  biz: string
  bizName?: string
  parentId?: number | null
  level: number
}

// 分类分页查询参数
export interface CategoryPageParams {
  pageNo: number
  pageSize: number
  biz?: string // 业务模块筛选
  level?: number // 分类层级：1-一级，2-二级，3-三级
  parentId?: number // 父级分类ID
}

// 分类分页查询结果
export interface CategoryPageResult {
  total: number
  list: QuestionCategory[]
}

// 分类列表查询参数
export interface CategoryListParams {
  biz?: string // 业务模块筛选
  level?: number // 分类层级筛选
  parentId?: number // 父级分类ID
}

// 分类列表查询结果
export interface CategoryListResult {
  list: QuestionCategory[]
}

// 分类列表查询结果（直接返回数组的情况）
export type CategoryListArray = QuestionCategory[]

// ==================== 考题管理API接口封装 ====================

export const QuestionManagementApi = {
  // ==================== 考题管理接口 ====================

  // 考题分页查询
  getQuestionPage: async (params: QuestionPageParams): Promise<QuestionPageResult> => {
    return await request.get({ url: '/publicbiz/question/page', params })
  },

  // 考题详情查询
  getQuestionDetail: async (id: number): Promise<Question> => {
    return await request.get({ url: `/publicbiz/question/get/${id}` })
  },

  // 新增考题
  addQuestion: async (data: AddQuestionParams): Promise<AddQuestionResult> => {
    return await request.post({ url: '/publicbiz/question/add', data })
  },

  // 编辑考题
  updateQuestion: async (data: UpdateQuestionParams): Promise<void> => {
    return await request.put({ url: '/publicbiz/question/update', data })
  },

  // 删除考题
  deleteQuestion: async (id: number): Promise<void> => {
    return await request.delete({ url: `/publicbiz/question/delete/${id}` })
  },

  // 考题统计报表
  getQuestionStatistics: async (params?: {
    biz?: string
    level1Name?: string
    level2Name?: string
    level3Name?: string
  }): Promise<QuestionStatisticsResult> => {
    return await request.get({ url: '/publicbiz/question/statistics', params })
  },

  // 批量导入考题
  batchImportQuestions: async (file: File): Promise<BatchImportResult> => {
    const formData = new FormData()
    formData.append('file', file)
    return await request.post({
      url: '/publicbiz/question/batch-import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 下载导入模板
  downloadTemplate: async (): Promise<Blob> => {
    return await request.get({
      url: '/publicbiz/question/download-template',
      responseType: 'blob'
    })
  },

  // ==================== 考题分类管理接口 ====================

  // 分类分页查询
  getCategoryPage: async (params: CategoryPageParams): Promise<CategoryPageResult> => {
    return await request.get({ url: '/publicbiz/question/category/page', params })
  },

  // 分类列表查询（不分页）
  getCategoryList: async (
    params?: CategoryListParams
  ): Promise<CategoryListResult | CategoryListArray> => {
    return await request.get({ url: '/publicbiz/question/category/list', params })
  },

  // 新增分类
  addCategory: async (data: AddCategoryParams): Promise<AddCategoryResult> => {
    return await request.post({ url: '/publicbiz/question/category/add', data })
  },

  // 编辑分类
  updateCategory: async (data: UpdateCategoryParams): Promise<void> => {
    return await request.put({ url: '/publicbiz/question/category/update', data })
  },

  // 删除分类
  deleteCategory: async (id: number): Promise<void> => {
    return await request.delete({ url: `/publicbiz/question/category/delete/${id}` })
  }
}
