<template>
  <div class="batch-import-content">
    <div class="import-title">批量导入考题</div>
   
    <el-form :model="form" :rules="rules" ref="formRef" label-width="0">
      <el-form-item prop="file" required>
        <div class="upload-label">选择Excel文件 <span style="color: #f56c6c">*</span></div>
        <el-upload
          class="upload-demo"
          :show-file-list="true"
          :auto-upload="false"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="form.fileList"
          accept=".xls,.xlsx"
        >
          <el-button>选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">请上传Excel格式文件(.xlsx或.xls)，文件大小不超过10MB</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="import-desc">
      <div class="desc-title">导入说明：</div>
      <div class="desc-content">
        <b>Excel文件格式要求：</b><br />
        <ul>
          <li>第一行为表头，不可修改</li>
          <li>一级名称、二级名称、三级名称、认定点名称、题型、题干、参考答案为必填字段</li>
          <li>题型包括：单选、多选、判断、简答、填空、材料题、排序题、匹配题、组合题</li>
          <li>单选题答案填写选项字母，如：A</li>
          <li>多选题答案用多分隔，如：A,C</li>
          <li>判断题答案填写：正确 或 错误</li>
          <li>填空题答案用"|"分隔多个空，如：答案1|答案2</li>
          <li>材料题在题干中包含阅读材料，子题目用分号分隔</li>
          <li>排序题选项用"|"分隔，答案填写正确顺序，如：C,A,B,D</li>
          <li>匹配题左右两列用"|"分隔，答案填写匹配关系，如：1-A,2-B,3-C</li>
          <li>文件上传需查看答案中说明并严格按要求</li>
        </ul>
        <b>注意：</b> 导入前请确保数据格式正确，错误的数据将被跳过。
      </div>
    </div>
    <div class="footer-btns">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
const emit = defineEmits(['close', 'submit'])
const formRef = ref()
const form = ref({
  file: null,
  fileList: [] as any[]
})
const rules = {
  file: [
    { required: true, message: '请选择Excel文件', trigger: 'change' }
  ]
}
function beforeUpload(file: File) {
  const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  return true
}
function handleFileChange(file: any, fileList: any[]) {
  form.value.file = fileList.length ? fileList[0].raw : null
  form.value.fileList = fileList
}
function onSubmit() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', form.value.file)
    }
  })
}
</script>

<style scoped lang="scss">
.batch-import-content {
  padding: 24px 32px 16px 32px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.import-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 0 0 12px 0;
}
.upload-label {
  font-weight: bold;
  margin-bottom: 4px;
}
.import-desc {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
  font-size: 14px;
  color: #333;
}
.desc-title {
  font-weight: bold;
  margin-bottom: 6px;
}
.desc-content {
  line-height: 1.8;
  ul {
    margin: 0 0 0 18px;
    padding: 0;
  }
  li {
    margin-bottom: 2px;
  }
}
.footer-btns {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}
</style>
